"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

interface FocusDistributionProps {
  data: Array<{
    hour: string
    value: number
  }>
}

interface CustomTooltipProps {
  active?: boolean
  payload?: Array<{ value: number }>
  label?: string
}

export function FocusDistribution({ data }: FocusDistributionProps) {
  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border border-slate-200/60 dark:border-slate-800/60 bg-gradient-to-br from-white via-white to-slate-50/30 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800/30 p-3 shadow-xl shadow-slate-900/5 dark:shadow-slate-900/20 backdrop-blur-sm">
          <p className="mb-1 font-medium text-slate-900 dark:text-slate-100">{label}</p>
          <p className="text-violet-600 dark:text-violet-400">
            <span className="font-bold">{payload[0].value}</span> minutes
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 10, left: 0, bottom: 20 }}>
          <XAxis
            dataKey="hour"
            stroke="currentColor"
            className="text-muted-foreground"
            tickLine={false}
            axisLine={false}
            tick={{ fontSize: 12 }}
            angle={-45}
            textAnchor="end"
            height={50}
          />
          <YAxis 
            stroke="currentColor" 
            className="text-muted-foreground" 
            tickLine={false} 
            axisLine={false} 
            tick={{ fontSize: 12 }} 
            width={30} 
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="value" fill="#5576F5" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}
