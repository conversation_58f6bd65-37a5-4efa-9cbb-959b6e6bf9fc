"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Responsive<PERSON>ontaine<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

interface WeeklyComparisonProps {
  data: Array<{
    name: string
    thisWeek: number
    lastWeek: number
  }>
}

interface CustomTooltipProps {
  active?: boolean
  payload?: Array<{ name: string; value: number; color: string }>
  label?: string
}

export function WeeklyComparison({ data }: WeeklyComparisonProps) {
  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border border-slate-200/60 dark:border-slate-800/60 bg-gradient-to-br from-white via-white to-slate-50/30 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800/30 p-3 shadow-xl shadow-slate-900/5 dark:shadow-slate-900/20 backdrop-blur-sm">
          <p className="mb-1 font-medium text-slate-900 dark:text-slate-100">{label}</p>
          {payload.map((entry, index) => (
            <p key={`tooltip-${index}`} style={{ color: entry.color }}>
              <span className="font-medium">{entry.name}: </span>
              <span className="font-bold">{entry.value}</span> minutes
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} margin={{ top: 5, right: 10, left: 0, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" vertical={false} />
          <XAxis 
            dataKey="name" 
            stroke="currentColor" 
            className="text-muted-foreground"
            tickLine={false} 
            axisLine={false} 
            tick={{ fontSize: 12 }} 
          />
          <YAxis 
            stroke="currentColor" 
            className="text-muted-foreground"
            tickLine={false} 
            axisLine={false} 
            tick={{ fontSize: 12 }} 
            width={30} 
          />
          <Tooltip content={<CustomTooltip />} />
          <Legend
            wrapperStyle={{ paddingTop: 10 }}
            formatter={(value) => <span className="text-xs text-muted-foreground">{value}</span>}
          />
          <Bar
            dataKey="thisWeek"
            name="This Week"
            fill="#5576F5"
            radius={[4, 4, 0, 0]}
          />
          <Bar dataKey="lastWeek" name="Last Week" fill="#36A3F2" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}
