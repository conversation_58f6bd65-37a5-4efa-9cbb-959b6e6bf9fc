"use client"

import { useState, useRef, useEffect } from "react"
import { createPortal } from "react-dom"

interface StreakCalendarProps {
  data: Array<{
    date: string
    count: number
  }>
  dailyFocusTime?: Array<{
    date: string
    minutes: number
  }>
}

export function StreakCalendar({ data, dailyFocusTime = [] }: StreakCalendarProps) {
  const [hoveredDay, setHoveredDay] = useState<null | { date: string; count: number; focusMinutes: number; x: number; y: number }>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Create focus time lookup map
  const focusTimeMap = new Map<string, number>()
  dailyFocusTime.forEach(day => {
    focusTimeMap.set(day.date, day.minutes)
  })

  // Get all months from data
  const getMonths = () => {
    const months: Record<string, Array<{ date: string; count: number }>> = {}

    data.forEach((day) => {
      const date = new Date(day.date)
      const monthKey = `${date.getFullYear()}-${date.getMonth() + 1}`

      if (!months[monthKey]) {
        months[monthKey] = []
      }

      months[monthKey].push(day)
    })

    return months
  }

  const months = getMonths()

  // Format month name
  const formatMonth = (monthKey: string) => {
    const [year, month] = monthKey.split("-")
    const date = new Date(Number.parseInt(year), Number.parseInt(month) - 1, 1)
    return date.toLocaleDateString("en-US", { month: "long", year: "numeric" })
  }

  // Get day of week (0-6, where 0 is Sunday)
  const getDayOfWeek = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.getDay()
  }

  // Get day of month
  const getDayOfMonth = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.getDate()
  }

  // Get color based on count
  const getColor = (count: number) => {
    if (count === 0) return "bg-muted"
    if (count < 2) return "bg-primary/20"
    if (count < 4) return "bg-primary/40"
    if (count < 6) return "bg-primary/60"
    return "bg-primary"
  }

  return (
    <div ref={containerRef} className="space-y-8">
      {Object.keys(months).map((monthKey) => {
        const monthData = months[monthKey]
        const firstDay = new Date(monthData[0].date)
        const firstDayOfWeek = getDayOfWeek(monthData[0].date)

        // Create array for days of the week
        const daysOfWeek = ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"]

        // Create a map of date -> count for quick lookup
        const dateMap: Record<number, number> = {}
        monthData.forEach((day) => {
          const dayOfMonth = getDayOfMonth(day.date)
          dateMap[dayOfMonth] = day.count
        })

        // Get days in month
        const daysInMonth = new Date(firstDay.getFullYear(), firstDay.getMonth() + 1, 0).getDate()

        // Create grid cells
        const cells = []

        // Add empty cells for days before the first day of the month
        for (let i = 0; i < firstDayOfWeek; i++) {
          cells.push(<div key={`empty-${i}`} className="h-8 w-8"></div>)
        }

        // Add cells for each day of the month
        for (let day = 1; day <= daysInMonth; day++) {
          const count = dateMap[day] || 0
          // Fix timezone issue: create date string directly instead of using toISOString()
          const year = firstDay.getFullYear()
          const month = (firstDay.getMonth() + 1).toString().padStart(2, '0')
          const dayStr = day.toString().padStart(2, '0')
          const date = `${year}-${month}-${dayStr}`

          cells.push(
            <div
              key={day}
              className={`flex h-8 w-8 cursor-pointer items-center justify-center rounded-full ${getColor(count)} text-xs transition-colors hover:opacity-80`}
              onMouseEnter={(e) => {
                const rect = e.currentTarget.getBoundingClientRect()
                const x = rect.left + rect.width / 2
                const y = rect.top
                setHoveredDay({ date, count, focusMinutes: focusTimeMap.get(date) || 0, x, y })
              }}
              onMouseLeave={() => setHoveredDay(null)}
            >
              {day}
            </div>,
          )
        }

        return (
          <div key={monthKey} className="relative">
            <h3 className="mb-2 text-sm font-medium">{formatMonth(monthKey)}</h3>
            <div className="mb-1 grid grid-cols-7 gap-1">
              {daysOfWeek.map((day) => (
                <div key={day} className="flex h-8 w-8 items-center justify-center text-xs text-muted-foreground">
                  {day}
                </div>
              ))}
            </div>
            <div className="grid grid-cols-7 gap-1">{cells}</div>


          </div>
        )
      })}

      {/* Legend */}
      <div className="flex items-center justify-end">
        <div className="mr-2 text-xs text-muted-foreground">Activity:</div>
        <div className="flex items-center gap-1">
          <div className="h-3 w-3 rounded-sm bg-muted"></div>
          <div className="h-3 w-3 rounded-sm bg-primary/20"></div>
          <div className="h-3 w-3 rounded-sm bg-primary/40"></div>
          <div className="h-3 w-3 rounded-sm bg-primary/60"></div>
          <div className="h-3 w-3 rounded-sm bg-primary"></div>
        </div>
        <div className="ml-2 flex items-center justify-between text-xs text-muted-foreground">
          <span>Less</span>
          <span className="ml-2">More</span>
        </div>
      </div>

      {/* Portal-based tooltip */}
      {mounted && hoveredDay && createPortal(
        <div
          className="fixed z-50 rounded-lg border border-slate-200/60 dark:border-slate-800/60 bg-gradient-to-br from-white via-white to-slate-50/30 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800/30 p-2 text-xs shadow-xl shadow-slate-900/5 dark:shadow-slate-900/20 backdrop-blur-sm pointer-events-none"
          style={{
            left: `${hoveredDay.x}px`,
            top: `${hoveredDay.y - 10}px`,
            transform: 'translateX(-50%) translateY(-100%)'
          }}
        >
          <p className="font-medium text-slate-900 dark:text-slate-100">
            {(() => {
              // Parse date string in local timezone to avoid timezone offset issues
              const [year, month, day] = hoveredDay.date.split('-').map(Number)
              const localDate = new Date(year, month - 1, day)
              return localDate.toLocaleDateString("en-US", {
                weekday: "long",
                month: "short",
                day: "numeric",
              })
            })()}
          </p>
          <div className="space-y-1">
            <p className="text-green-600 dark:text-green-400">{hoveredDay.count} pomodoros completed</p>
            {hoveredDay.focusMinutes > 0 && (
              <p className="text-emerald-600 dark:text-emerald-400">
                {Math.floor(hoveredDay.focusMinutes / 60) > 0
                  ? `${Math.floor(hoveredDay.focusMinutes / 60)}h ${hoveredDay.focusMinutes % 60}m`
                  : `${hoveredDay.focusMinutes}m`} focus time
              </p>
            )}
          </div>
        </div>,
        document.body
      )}
    </div>
  )
}

