"use client"

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "recharts"

interface CompletionRateChartProps {
  data: {
    completed: number
    interrupted: number
  }
}

interface CustomTooltipProps {
  active?: boolean
  payload?: Array<{ name: string; value: number; payload: { color: string } }>
}

export function CompletionRateChart({ data }: CompletionRateChartProps) {
  const chartData = [
    { name: "Completed", value: data.completed, color: "#3A6CF4" },
    { name: "Interrupted", value: data.interrupted, color: "#F48B3A" },
  ]

  const totalSessions = data.completed + data.interrupted
  const completionRate = totalSessions > 0 ? Math.round((data.completed / totalSessions) * 100) : 0

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border border-slate-200/60 dark:border-slate-800/60 bg-gradient-to-br from-white via-white to-slate-50/30 dark:from-slate-900 dark:via-slate-900 dark:to-slate-800/30 p-3 shadow-xl shadow-slate-900/5 dark:shadow-slate-900/20 backdrop-blur-sm">
          <p className="mb-1 font-medium text-slate-900 dark:text-slate-100">{payload[0].name} Sessions</p>
          <p style={{ color: payload[0].payload.color }}>
            <span className="font-bold">{payload[0].value}</span> sessions (
            {Math.round((payload[0].value / totalSessions) * 100)}%)
          </p>
        </div>
      )
    }
    return null
  }

  return (
    <div className="flex h-[300px] flex-col items-center justify-center">
      <div className="mb-4 text-center">
        <p className="text-sm text-muted-foreground">Completion Rate</p>
        <p className="text-3xl font-bold">{completionRate}%</p>
      </div>

      <div className="h-[200px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie data={chartData} cx="50%" cy="50%" innerRadius={60} outerRadius={80} paddingAngle={5} dataKey="value">
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
          </PieChart>
        </ResponsiveContainer>
      </div>

      <div className="mt-4 flex justify-center gap-6">
        <div className="flex items-center">
          <div className="mr-2 h-3 w-3 rounded-full" style={{ backgroundColor: "#3A6CF4" }}></div>
          <span className="text-xs text-muted-foreground">Completed</span>
        </div>
        <div className="flex items-center">
          <div className="mr-2 h-3 w-3 rounded-full" style={{ backgroundColor: "#F48B3A" }}></div>
          <span className="text-xs text-muted-foreground">Interrupted</span>
        </div>
      </div>
    </div>
  )
}
