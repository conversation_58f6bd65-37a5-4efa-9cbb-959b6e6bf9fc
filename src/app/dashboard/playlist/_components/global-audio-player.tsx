"use client"

import { useEffect, useRef, useCallback, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { useSidebar } from "@/components/ui/sidebar"
import {
  Play,
  Pause,
  Volume2,
  VolumeX,
  X,
  Music2,
  Waves
} from "lucide-react"
import { useAudioStore } from "@/lib/audio-store"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"

export function GlobalAudioPlayer() {
  const {
    globalPlayer,
    stopGlobalPlayer,
    setGlobalPlayerPlaying,
  } = useAudioStore()

  const { state: sidebarState, isMobile } = useSidebar()
  const audioRef = useRef<HTMLAudioElement | null>(null)

  // Local state for audio playback (like the working admin player)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(80)
  const [isMuted, setIsMuted] = useState(false)

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Handle time update event (using local state like admin player)
  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime)
    }
  }, [])

  // Handle loaded metadata event (using local state like admin player)
  const handleLoadedMetadata = useCallback(() => {
    if (audioRef.current && !isNaN(audioRef.current.duration)) {
      setDuration(audioRef.current.duration)
      setCurrentTime(0) // Ensure current time is reset
    }
  }, [])

  // Handle play/pause toggle (using local state like admin player)
  const handlePlayPause = useCallback(() => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
    } else {
      audioRef.current.play().catch(error => {
        console.error("Play failed:", error)
      })
    }
  }, [isPlaying])

  // Sync local isPlaying state with global store when external commands change it
  useEffect(() => {
    if (!audioRef.current) return

    if (globalPlayer.isPlaying && !isPlaying) {
      // External command to play
      audioRef.current.play().catch(error => {
        console.error("Play failed:", error)
        setGlobalPlayerPlaying(false)
      })
    } else if (!globalPlayer.isPlaying && isPlaying) {
      // External command to pause
      audioRef.current.pause()
    }
  }, [globalPlayer.isPlaying, isPlaying, setGlobalPlayerPlaying])

  // Handle volume toggle
  const handleVolumeToggle = useCallback(() => {
    setIsMuted(!isMuted)
  }, [isMuted])

  // Handle seeking in the timeline
  const handleSeek = useCallback((value: number[]) => {
    const newTime = value[0]
    if (audioRef.current) {
      audioRef.current.currentTime = newTime
      setCurrentTime(newTime)
    }
  }, [])

  // Handle close player
  const handleClose = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause()
    }
    stopGlobalPlayer()
  }, [stopGlobalPlayer])

  // Create/update audio element and set up event listeners (like admin player)
  useEffect(() => {
    if (!globalPlayer.currentTrack?.src) {
      setIsPlaying(false)
      setCurrentTime(0)
      setDuration(0)
      return
    }

    // Reset state when track changes
    setCurrentTime(0)
    setDuration(0)

    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      audioRef.current = new Audio()
    }

    // Define event handlers with stable references
    const handlePlay = () => {
      setIsPlaying(true)
      setGlobalPlayerPlaying(true)
    }

    const handlePause = () => {
      setIsPlaying(false)
      setGlobalPlayerPlaying(false)
    }

    const handleEnded = () => {
      setIsPlaying(false)
      stopGlobalPlayer()
    }

    // Remove any existing event listeners first
    audioRef.current.removeEventListener("play", handlePlay)
    audioRef.current.removeEventListener("pause", handlePause)
    audioRef.current.removeEventListener("ended", handleEnded)
    audioRef.current.removeEventListener("timeupdate", handleTimeUpdate)
    audioRef.current.removeEventListener("loadedmetadata", handleLoadedMetadata)

    // Add fresh event listeners
    audioRef.current.addEventListener("play", handlePlay)
    audioRef.current.addEventListener("pause", handlePause)
    audioRef.current.addEventListener("ended", handleEnded)
    audioRef.current.addEventListener("timeupdate", handleTimeUpdate)
    audioRef.current.addEventListener("loadedmetadata", handleLoadedMetadata)

    // Update audio source if it changed
    if (audioRef.current.src !== globalPlayer.currentTrack.src) {
      audioRef.current.src = globalPlayer.currentTrack.src
      audioRef.current.load()
      // Reset time values immediately when loading new track
      setCurrentTime(0)
      setDuration(0)
    }

    // Play the audio
    const playPromise = audioRef.current.play()

    // Handle autoplay restrictions
    if (playPromise !== undefined) {
      playPromise.catch(error => {
        console.error("Autoplay prevented:", error)
        setIsPlaying(false)
      })
    }

    // Update volume & muted state
    audioRef.current.volume = isMuted ? 0 : volume / 100

    // Cleanup function
    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener("play", handlePlay)
        audioRef.current.removeEventListener("pause", handlePause)
        audioRef.current.removeEventListener("ended", handleEnded)
        audioRef.current.removeEventListener("timeupdate", handleTimeUpdate)
        audioRef.current.removeEventListener("loadedmetadata", handleLoadedMetadata)
        audioRef.current.pause()
      }
    }
  }, [globalPlayer.currentTrack?.src, stopGlobalPlayer, isMuted, volume, handleTimeUpdate, handleLoadedMetadata, setGlobalPlayerPlaying])

  // Reset progress when track changes (track ID change)
  useEffect(() => {
    if (globalPlayer.currentTrack?.id) {
      setCurrentTime(0)
      setDuration(0)
    }
  }, [globalPlayer.currentTrack?.id])

  // Update volume when it changes (like admin player)
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume / 100
    }
  }, [volume, isMuted])

  if (!globalPlayer.currentTrack) {
    return null
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        exit={{ y: 100, opacity: 0 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="fixed bottom-0 z-[9999] bg-background/95 backdrop-blur-sm border-t border-border shadow-lg transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]"
        style={{
          left: isMobile ? '0' : (sidebarState === "collapsed" ? 'var(--sidebar-width-icon, 3rem)' : 'var(--sidebar-width, 16rem)'),
          right: '0',
        }}
      >
        <div className="w-full p-4">
          <div className="flex items-center gap-4">
            {/* Play/Pause Button */}
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-10 w-10 rounded-full shrink-0 transition-all duration-200",
                isPlaying
                  ? "bg-gradient-to-r from-orange-500 to-rose-500 text-white hover:from-orange-600 hover:to-rose-600"
                  : "hover:bg-muted"
              )}
              onClick={handlePlayPause}
            >
              {isPlaying ? (
                <Pause className="h-4 w-4" />
              ) : (
                <Play className="h-4 w-4 ml-0.5" />
              )}
            </Button>

            {/* Track Icon */}
            <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30 flex items-center justify-center shrink-0">
              {globalPlayer.currentTrack.type === "music" ? (
                <Music2 className="h-4 w-4 text-orange-600 dark:text-orange-400" />
              ) : (
                <Waves className="h-4 w-4 text-orange-600 dark:text-orange-400" />
              )}
            </div>

            {/* Track Info */}
            <div className="flex-1 min-w-0">
              <div className="truncate font-medium text-sm">
                {globalPlayer.currentTrack.title}
              </div>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                {globalPlayer.currentTrack.type === "music" && globalPlayer.currentTrack.genres && globalPlayer.currentTrack.genres.length > 0 && (
                  <>
                    <Badge variant="secondary" className="text-xs">
                      {globalPlayer.currentTrack.genres[0]}
                    </Badge>
                    <span>•</span>
                  </>
                )}
                {globalPlayer.currentTrack.type === "nature-sound" && globalPlayer.currentTrack.category && globalPlayer.currentTrack.category.length > 0 && (
                  <>
                    <Badge variant="secondary" className="text-xs">
                      {globalPlayer.currentTrack.category[0]}
                    </Badge>
                    <span>•</span>
                  </>
                )}
                <span className="text-muted-foreground">Track</span>
              </div>
            </div>

            {/* Center Progress Bar with Time Display */}
            <div className="hidden md:flex flex-1 max-w-80 items-center justify-center gap-3">
              <span className="text-xs text-muted-foreground font-mono min-w-[2.5rem] text-right">
                {formatTime(currentTime)}
              </span>
              <Slider
                value={[currentTime]}
                min={0}
                max={duration || 100}
                step={0.1}
                onValueChange={handleSeek}
                className="cursor-pointer flex-1"
              />
              <span className="text-xs text-muted-foreground font-mono min-w-[2.5rem] text-left">
                {formatTime(duration)}
              </span>
            </div>

            {/* Volume Controls */}
            <div className="flex items-center gap-2 shrink-0">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleVolumeToggle}
              >
                {isMuted ? (
                  <VolumeX className="h-4 w-4" />
                ) : (
                  <Volume2 className="h-4 w-4" />
                )}
              </Button>
              <Slider
                value={[isMuted ? 0 : volume]}
                min={0}
                max={100}
                step={1}
                onValueChange={(value) => {
                  setVolume(value[0])
                  if (value[0] > 0 && isMuted) {
                    setIsMuted(false)
                  }
                }}
                className="w-20"
              />
            </div>

            {/* Close Button */}
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handleClose}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Mobile Progress Bar with Time Display */}
          <div className="md:hidden mt-3 space-y-2">
            <div className="flex items-center justify-center gap-3">
              <span className="text-xs text-muted-foreground font-mono">
                {formatTime(currentTime)}
              </span>
              <Slider
                value={[currentTime]}
                min={0}
                max={duration || 100}
                step={0.1}
                onValueChange={handleSeek}
                className="cursor-pointer flex-1"
              />
              <span className="text-xs text-muted-foreground font-mono">
                {formatTime(duration)}
              </span>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  )
}
