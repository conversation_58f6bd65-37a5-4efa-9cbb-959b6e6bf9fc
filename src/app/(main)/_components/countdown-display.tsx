'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { usePomodoroStore } from '@/lib/pomodoro-store';

interface CountdownDisplayProps {
  initialMinutes?: number;
  className?: string;
  onComplete?: () => void;
}

export const CountdownDisplay = ({
  initialMinutes = 25,
  className = "",
  onComplete
}: CountdownDisplayProps) => {
  // Get timer mode from the pomodoro store
  const timerMode = usePomodoroStore((state) => state.timerSettings.timerMode);

  const [timeLeft, setTimeLeft] = useState({
    minutes: timerMode === 'countUp' ? 0 : initialMinutes,
    seconds: 0
  });
  const [isMinuteFlipping, setIsMinuteFlipping] = useState(false);
  const [isSecondFlipping, setIsSecondFlipping] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Timer functionality that starts automatically (supports both count up and count down)
  useEffect(() => {
    // Start the timer
    timerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (timerMode === 'countUp') {
          // Count up mode: increment time
          if (prev.seconds === 59) {
            // Increment minutes and reset seconds to 0
            setIsMinuteFlipping(true);
            setTimeout(() => setIsMinuteFlipping(false), 300);
            return {
              minutes: prev.minutes + 1,
              seconds: 0
            };
          }
          // Otherwise just increment seconds
          return {
            ...prev,
            seconds: prev.seconds + 1
          };
        } else {
          // Count down mode: decrement time
          if (prev.seconds === 0) {
            // If minutes is also 0, reset to initial time and trigger onComplete callback
            if (prev.minutes === 0) {
              onComplete?.();
              return {
                minutes: initialMinutes,
                seconds: 0
              };
            }
            // Otherwise decrement minutes and set seconds to 59
            setIsMinuteFlipping(true);
            setTimeout(() => setIsMinuteFlipping(false), 300);
            return {
              minutes: prev.minutes - 1,
              seconds: 59
            };
          }
          // Otherwise just decrement seconds
          return {
            ...prev,
            seconds: prev.seconds - 1
          };
        }
      });

      // Add flipping animation effect when seconds change
      setIsSecondFlipping(true);
      setTimeout(() => setIsSecondFlipping(false), 300);
    }, 1000);

    // Cleanup interval on component unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [initialMinutes, onComplete, timerMode]);

  // Update minutes when prop changes or timer mode changes
  useEffect(() => {
    setTimeLeft({
      minutes: timerMode === 'countUp' ? 0 : initialMinutes,
      seconds: 0
    });
  }, [initialMinutes, timerMode]);

  const formatDigit = (value: number): string => {
    return String(value).padStart(2, '0');
  };

  return (
    <div className={cn(
      "flex items-center justify-center space-x-2 px-4 py-3 rounded-lg",
      "bg-gradient-to-br from-rose-50/90 to-white/95 dark:from-slate-800 dark:to-slate-900/90",
      "shadow-md shadow-rose-100/20 dark:shadow-slate-900/20",
      "border-2 border-rose-100/60 dark:border-slate-700/60",
      "transition-all duration-300 ease-in-out",
      className
    )}>
      {/* Minutes */}
      <div className="flex flex-col items-center">
        <div
          className={cn(
            "relative overflow-hidden transition-all duration-300 ease-in-out",
            "rounded-md",
            isMinuteFlipping ? "transform -translate-y-1" : ""
          )}
        >
          <span
            suppressHydrationWarning
            className="text-3xl md:text-4xl font-bold text-red-500 dark:text-red-400 tabular-nums"
          >
            {formatDigit(timeLeft.minutes)}
          </span>
        </div>
        <span className="text-[10px] uppercase text-slate-500 dark:text-slate-400 tracking-wider font-medium mt-0.5">
          min
        </span>
      </div>

      {/* Separator */}
      <div className="flex flex-col items-center justify-center">
        <span className="text-2xl md:text-3xl font-medium text-slate-400 dark:text-slate-500 animate-pulse">:</span>
        <div className="h-5"></div> {/* Spacer to align with the labels */}
      </div>

      {/* Seconds */}
      <div className="flex flex-col items-center">
        <div
          className={cn(
            "relative overflow-hidden transition-all duration-300 ease-in-out",
            "rounded-md",
            isSecondFlipping ? "transform -translate-y-1" : ""
          )}
        >
          <span
            suppressHydrationWarning
            className="text-3xl md:text-4xl font-bold text-slate-700 dark:text-slate-300 tabular-nums"
          >
            {formatDigit(timeLeft.seconds)}
          </span>
        </div>
        <span className="text-[10px] uppercase text-slate-500 dark:text-slate-400 tracking-wider font-medium mt-0.5">
          sec
        </span>
      </div>
    </div>
  );
};