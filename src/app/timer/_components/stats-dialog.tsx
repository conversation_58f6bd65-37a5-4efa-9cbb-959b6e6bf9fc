'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTrigger, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { BarChart3, Clock, Target, Flame, TrendingUp, LogIn, Loader2, AlertTriangle, Cloud } from 'lucide-react';
import { motion } from 'framer-motion';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useUserStore } from '@/store/userStore';
import { useGetPomodoroQuickStats } from '../../../../prisma/schema/Pomodoro/pomodoro-query';
import { useLocalPomodoroStore } from '@/lib/local-pomodoro-store';
import { useLocalTaskStore } from '@/lib/local-task-store';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface StatsDialogProps {
  className?: string;
  variant?: 'video-background' | 'header-summary';
  showTooltip?: boolean;
  isMobile?: boolean;
}

export function StatsDialog({ className, variant = 'video-background', showTooltip = true, isMobile = false }: StatsDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { isAuthenticated } = useUserStore();

  // Fetch database stats for authenticated users only
  const { data: quickStatsData, isLoading, error } = useGetPomodoroQuickStats(isAuthenticated);

  // Get local stats for unauthenticated users
  const { getQuickStats } = useLocalPomodoroStore();
  const { getTaskById } = useLocalTaskStore();
  const localStats = getQuickStats();

  // Helper function to populate session titles with task names for local data
  const populateLocalSessionTitles = (sessions: any[]) => {
    return sessions.map(session => {
      if (session.taskId && !isAuthenticated) {
        const task = getTaskById(session.taskId);
        if (task) {
          return {
            ...session,
            title: task.title
          };
        }
      }
      return session;
    });
  };

  // Format focus time (in minutes) to hours and minutes
  const formatFocusTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${remainingMinutes}m`;
    }
    return `${minutes}m`;
  };

  // Format time to HH:MM
  const formatTime = (timeString: string) => {
    try {
      const date = new Date(timeString);
      return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '--:--';
    }
  };

  // Get session type display info
  const getSessionTypeInfo = (type: string) => {
    switch (type.toLowerCase()) {
      case 'focus':
        return {
          label: 'Focus',
          color: 'bg-blue-500',
          textColor: 'text-blue-600 dark:text-blue-400',
          bgColor: 'bg-blue-500/10'
        };
      case 'shortbreak':
      case 'short_break':
        return {
          label: 'Short Break',
          color: 'bg-green-500',
          textColor: 'text-green-600 dark:text-green-400',
          bgColor: 'bg-green-500/10'
        };
      case 'longbreak':
      case 'long_break':
        return {
          label: 'Long Break',
          color: 'bg-purple-500',
          textColor: 'text-purple-600 dark:text-purple-400',
          bgColor: 'bg-purple-500/10'
        };
      default:
        return {
          label: 'Session',
          color: 'bg-gray-500',
          textColor: 'text-gray-600 dark:text-gray-400',
          bgColor: 'bg-gray-500/10'
        };
    }
  };

  // Extract data from appropriate source (local vs database)
  const getStatsData = () => {
    if (isAuthenticated) {
      return {
        todayFocusTime: quickStatsData?.todayFocusTime || 0,
        todayCompletedSessions: quickStatsData?.todayCompletedSessions || 0,
        todaySessions: quickStatsData?.todaySessions || [],
        weekTotalDuration: quickStatsData?.weekTotalDuration || 0,
        weekCompletionRate: quickStatsData?.weekCompletionRate || 0,
        hasData: true, // Always show stats for authenticated users
        dateRange: quickStatsData?.dateRange
      };
    } else {
      return {
        todayFocusTime: localStats.todayFocusTime,
        todayCompletedSessions: localStats.todayCompletedSessions,
        todaySessions: populateLocalSessionTitles(localStats.todaySessions),
        weekTotalDuration: localStats.weekTotalDuration,
        weekCompletionRate: localStats.weekCompletionRate,
        hasData: true, // Always show stats, even with 0 values
        dateRange: localStats.dateRange
      };
    }
  };

  const statsData = getStatsData();

  const renderLocalStatsPrompt = () => (
    <div className="space-y-3 p-2">
      <div className="flex items-center gap-2 p-2 rounded-lg bg-gradient-to-r from-blue-50/80 to-indigo-50/80 dark:from-blue-950/20 dark:to-indigo-950/20 border border-blue-200/50 dark:border-blue-800/30">
        <div className="flex items-center gap-1.5">
          <Cloud className="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
        </div>
        <div className="flex-1">
          <p className="text-xs text-blue-600/80 dark:text-blue-400/80">
            Sign in to sync across devices
          </p>
        </div>
      </div>

      <Link href="/auth/sign-in" className="block">
        <Button size="sm" className="w-full text-xs h-8 cursor-pointer">
          <LogIn className="h-3 w-3 mr-2" />
          Sign In to Sync Data
        </Button>
      </Link>
    </div>
  );

  const renderStatsContent = () => {
    // Filter to get all focus sessions (for consistent counting)
    const allFocusSessions = statsData.todaySessions.filter(session =>
      session.type.toLowerCase() === 'focus'
    );

    // Calculate completion statistics
    const completedSessions = allFocusSessions.filter(session => session.completed).length;
    const interruptedSessions = allFocusSessions.filter(session => session.interrupted).length;
    const totalSessions = allFocusSessions.length;

    const metrics = [
      {
        label: "Today's Focus",
        value: formatFocusTime(statsData.todayFocusTime),
        icon: Clock,
        color: "text-green-600 dark:text-green-400",
        bgColor: "bg-green-500/10"
      },
      {
        label: "Focus Sessions",
        value: allFocusSessions.length.toString(),
        icon: Target,
        color: "text-blue-600 dark:text-blue-400",
        bgColor: "bg-blue-500/10"
      },
      {
        label: "Week Total",
        value: formatFocusTime(statsData.weekTotalDuration),
        icon: TrendingUp,
        color: "text-purple-600 dark:text-purple-400",
        bgColor: "bg-purple-500/10",
        tooltip: statsData.dateRange ? `Week from ${statsData.dateRange.weekStart} to ${statsData.dateRange.today}` : "Week total (local data)"
      },
      {
        label: "Completion Rate",
        value: statsData.weekCompletionRate + "%",
        subValue: totalSessions > 0 ? `(${interruptedSessions}/${totalSessions})` : null,
        icon: Flame,
        color: "text-amber-600 dark:text-amber-400",
        bgColor: "bg-amber-500/10",
        tooltip: totalSessions > 0 
          ? `${completedSessions} completed, ${interruptedSessions} interrupted out of ${totalSessions} total sessions today`
          : "No sessions yet today"
      }
    ];

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-sm">Your Focus Stats</h3>
            <p className="text-xs text-muted-foreground">
              {isAuthenticated ? 'Last 7 days' : 'Last 7 days'}
            </p>
          </div>
          <Link href="/dashboard">
            <Button variant="ghost" size="sm" className="text-xs h-7 cursor-pointer">
              View All
            </Button>
          </Link>
        </div>

        <div className="grid grid-cols-2 gap-3">
          {metrics.map((metric) => {
            const IconComponent = metric.icon;
            const hasTooltip = metric.tooltip;
            
            const metricCard = (
              <div
                key={metric.label}
                className={cn(
                  "rounded-lg border p-3 transition-all duration-200 hover:shadow-sm",
                  "bg-gradient-to-br from-background to-muted/20",
                  hasTooltip && "cursor-help"
                )}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className={cn("rounded-md p-1.5", metric.bgColor)}>
                    <IconComponent className={cn("h-3 w-3", metric.color)} />
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="flex items-baseline gap-1">
                    <p className="text-lg font-bold tracking-tight">{metric.value}</p>
                    {metric.subValue && (
                      <span className="text-xs text-muted-foreground font-medium">
                        {metric.subValue}
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">{metric.label}</p>
                </div>
              </div>
            );

            // Wrap with tooltip if available
            if (hasTooltip) {
              return (
                <Tooltip key={metric.label}>
                  <TooltipTrigger asChild>
                    {metricCard}
                  </TooltipTrigger>
                  <TooltipContent side="top" className="max-w-48 text-center">
                    <p className="text-xs leading-relaxed">{metric.tooltip}</p>
                  </TooltipContent>
                </Tooltip>
              );
            }

            return metricCard;
          })}
        </div>

        {(() => {
          // Use the same focus sessions array for consistency
          const focusSessions = allFocusSessions;

          return focusSessions.length > 0 && (
            <div className="pt-3 border-t border-border/50">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-xs font-semibold text-foreground">Recent Focus Sessions</h4>
                <span className="text-xs text-muted-foreground">
                  {focusSessions.length} today
                </span>
              </div>
              <div className="space-y-2 max-h-24 overflow-y-auto scrollbar-thin scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent">
                {focusSessions.slice(0, 4).map((session, sessionIndex) => {
                const typeInfo = getSessionTypeInfo(session.type);
                const startTime = formatTime(session.startTime);
                const endTime = formatTime(session.endTime);

                return (
                  <div
                    key={session.id || sessionIndex}
                    className={cn(
                      "group relative rounded-md p-2.5 transition-all duration-200",
                      "hover:bg-muted/50 hover:shadow-sm border hover:border-border/30",
                      "bg-gradient-to-r from-background to-muted/20",
                      // Special styling for interrupted sessions
                      session.interrupted
                        ? "border-amber-200/50 dark:border-amber-800/30 bg-gradient-to-r from-amber-50/30 to-amber-100/20 dark:from-amber-950/20 dark:to-amber-900/10"
                        : "border-transparent"
                    )}
                  >
                    <div className="flex items-start justify-between gap-2">
                      {/* Session Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <div className={cn("w-1.5 h-1.5 rounded-full flex-shrink-0", typeInfo.color)} />
                          <span className="text-xs font-medium text-foreground truncate">
                            {session.title || `${typeInfo.label} Session`}
                          </span>
                          {/* Interrupted indicator */}
                          {session.interrupted && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <div className="flex items-center justify-center w-4 h-4 rounded-full bg-amber-500/15 flex-shrink-0">
                                  <AlertTriangle className="w-2.5 h-2.5 text-amber-600 dark:text-amber-400" />
                                </div>
                              </TooltipTrigger>
                              <TooltipContent side="top" className="text-xs">
                                <span>Session was interrupted</span>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </div>

                        {/* Timeline */}
                        <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                          <Clock className="w-3 h-3 flex-shrink-0" />
                          <span className="font-mono">
                            {startTime} - {endTime}
                          </span>
                          <span className="text-muted-foreground/60">•</span>
                          <span className="font-medium">
                            {session.duration}m
                          </span>
                        </div>
                      </div>

                      {/* Status Indicator */}
                      <div className="flex items-center gap-1.5 flex-shrink-0">
                        <div className={cn(
                          "flex items-center justify-center w-5 h-5 rounded-full text-xs font-medium transition-colors",
                          session.completed
                            ? "bg-green-500/15 text-green-600 dark:text-green-400"
                            : session.interrupted
                            ? "bg-red-500/15 text-red-600 dark:text-red-400"
                            : "bg-amber-500/15 text-amber-600 dark:text-amber-400"
                        )}>
                          {session.completed ? "✓" : session.interrupted ? "✕" : "○"}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

              {/* Show more indicator if there are more focus sessions */}
              {focusSessions.length > 4 && (
                <div className="mt-2 text-center">
                  <Link href="/dashboard">
                    <Button variant="ghost" size="sm" className="text-xs h-6 px-2 text-muted-foreground hover:text-foreground">
                      +{focusSessions.length - 4} more focus sessions
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          );
        })()}

        {/* Show local data sync prompt for unauthenticated users */}
        {!isAuthenticated && (
          <div className="pt-3 border-t border-border/50">
            {renderLocalStatsPrompt()}
          </div>
        )}
      </div>
    );
  };

  // Determine button styling based on variant
  const getButtonStyles = () => {
    if (variant === 'video-background') {
      return {
        buttonClass: cn(
          "rounded-full bg-transparent backdrop-blur-lg border-white/30 hover:bg-white/10 transition-all duration-200 cursor-pointer shadow-lg shadow-black/20",
          isOpen && "bg-white/15",
          isMobile && "h-9 w-9 p-0",
          className
        ),
        iconClass: isMobile ? "h-3 w-3 text-white drop-shadow-md" : "h-4 w-4 text-white drop-shadow-md",
        motionProps: {
          whileHover: !isMobile ? {
            scale: 1.1,
            boxShadow: "0 0 12px rgba(255, 255, 255, 0.6)",
            backgroundColor: "rgba(255, 255, 255, 0.12)"
          } : {},
          whileTap: {
            scale: 0.95,
            backgroundColor: "rgba(255, 255, 255, 0.18)",
            rotate: isMobile ? 0 : 90
          },
          transition: {
            type: "spring",
            stiffness: 400,
            damping: 17,
            rotate: {
              type: "spring",
              stiffness: 300,
              damping: 20
            }
          }
        }
      };
    } else {
      return {
        buttonClass: cn(
          "h-9 w-9 sm:h-8 sm:w-8 hover:bg-primary/10 hover:border-primary/20 transition-colors cursor-pointer touch-manipulation",
          className
        ),
        iconClass: "h-4 w-4",
        motionProps: {} // No motion for header summary
      };
    }
  };

  const buttonStyles = getButtonStyles();

  // Render button with or without tooltip based on showTooltip prop
  const renderButton = () => {
    const buttonElement = (
      <motion.div
        {...(variant === 'video-background' ? buttonStyles.motionProps : {})}
        className={variant === 'video-background' ? "rounded-full overflow-hidden" : ""}
      >
        <Button
          variant="outline"
          size={variant === 'video-background' && isMobile ? "default" : "icon"}
          className={buttonStyles.buttonClass}
        >
          <BarChart3 className={buttonStyles.iconClass} />
        </Button>
      </motion.div>
    );

    if (showTooltip && variant === 'video-background') {
      return (
        <Tooltip>
          <TooltipTrigger asChild>
            {buttonElement}
          </TooltipTrigger>
          <TooltipContent side="left" className="bg-background/90 backdrop-blur-sm text-foreground">
            <span className="font-medium tracking-wide">Focus statistics</span>
          </TooltipContent>
        </Tooltip>
      );
    }

    return buttonElement;
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div>
          {renderButton()}
        </div>
      </DialogTrigger>

      <DialogContent className="w-full max-w-md mx-auto bg-background/95 backdrop-blur-md border-white/20 p-0 gap-0 max-h-[90vh] overflow-hidden">
        <DialogHeader className="px-4 pt-4 pb-2">
          <DialogTitle className="text-base font-semibold flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Focus Statistics
          </DialogTitle>
        </DialogHeader>

        <div className="px-4 pb-4 overflow-y-auto max-h-[calc(90vh-4rem)]">
          {isAuthenticated && isLoading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </div>
          ) : isAuthenticated && (error || !quickStatsData) ? (
            <div className="space-y-4 text-center p-2">
              <div className="space-y-2">
                <div className="mx-auto w-12 h-12 bg-gradient-to-br from-muted/20 to-muted/10 rounded-full flex items-center justify-center">
                  <BarChart3 className="h-6 w-6 text-muted-foreground" />
                </div>
                <h3 className="font-semibold text-sm">Unable to Load Stats</h3>
                <p className="text-xs text-muted-foreground leading-relaxed">
                  Please try again or start a focus session.
                </p>
              </div>
            </div>
          ) : (
            renderStatsContent()
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Backward compatibility export
export const StatsPopover = StatsDialog;