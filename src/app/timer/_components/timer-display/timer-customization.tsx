'use client';

import { But<PERSON> } from '@/components/ui/button';
import { usePomodoroStore, TimerColorPreset, TimerUIStyle } from '@/lib/pomodoro-store';
import { cn } from '@/lib/utils';
import { Check, Circle } from 'lucide-react';
import { useCallback } from 'react';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';

export function TimerCustomization() {
  const timerColor = usePomodoroStore((state) => state.timerColor);
  const setTimerColor = usePomodoroStore((state) => state.setTimerColor);
  const timerOpacity = usePomodoroStore((state) => state.timerOpacity);
  const setTimerOpacity = usePomodoroStore((state) => state.setTimerOpacity);
  
  // Add timer UI style state - keep these for type reference
  const timerUIStyle = usePomodoroStore((state) => state.timerUIStyle);
  const setTimerUIStyle = usePomodoroStore((state) => state.setTimerUIStyle);
  
  // Define color options
  const colorOptions: Array<{ value: TimerColorPreset; label: string }> = [
    { value: 'default', label: 'Default' },
    { value: 'white', label: 'White' },
    { value: 'blue', label: 'Blue' },
    { value: 'green', label: 'Green' },
    { value: 'yellow', label: 'Yellow' },
    { value: 'red', label: 'Red' },
    { value: 'purple', label: 'Purple' },
    // { value: 'indigo', label: 'Indigo' },
    { value: 'pink', label: 'Pink' },
    { value: 'orange', label: 'Orange' },
  ];
  
  const handleOpacityChange = useCallback((value: number[]) => {
    setTimerOpacity(value[0]);
  }, [setTimerOpacity]);
  
  return (
    <div className="space-y-4">
      {/* Color Options Section */}
      <div className="space-y-1.5">
        <div className="flex items-center justify-between">
          <h3 className="text-xs font-medium text-foreground flex items-center gap-1">
            <Circle className="h-3 w-3 fill-primary/10 text-primary/70" />
            <span>Text Color</span>
          </h3>
          <Badge variant="outline" className="text-[9px] px-1 py-0 h-4 font-normal border-primary/20 bg-background capitalize">
            {timerColor}
          </Badge>
        </div>
        <div className="bg-muted/30 p-2.5 rounded-lg border border-border/40">
          <div className="grid grid-cols-4 gap-1.5">
            {colorOptions.map((option) => {
              const colorClasses: Record<TimerColorPreset, string> = {
                default: 'bg-gradient-to-r from-blue-500 via-green-500 to-purple-500',
                white: 'bg-white',
                blue: 'bg-blue-500',
                green: 'bg-emerald-500',
                yellow: 'bg-amber-400',
                red: 'bg-rose-500',
                purple: 'bg-purple-500',
                indigo: 'bg-indigo-500',
                pink: 'bg-pink-500',
                orange: 'bg-orange-500' // Added for type safety
              };
              
              return (
                <Button
                  key={option.value}
                  variant={timerColor === option.value ? "default" : "outline"}
                  className={cn(
                    "h-7 py-0 px-1 relative justify-center items-center rounded-md transition-all",
                    timerColor === option.value 
                      ? "border-primary/30 bg-primary/10 text-primary shadow-sm hover:bg-primary/20 hover:text-primary hover:border-primary/40" 
                      : "bg-background border-border/60 hover:bg-muted/50 hover:text-foreground hover:border-border"
                  )}
                  onClick={() => setTimerColor(option.value)}
                >
                  <div className="flex items-center gap-1.5">
                    <div className={cn(
                      "w-2.5 h-2.5 rounded-full",
                      colorClasses[option.value]
                    )} />
                    <span className="text-[9px]">{option.label}</span>
                  </div>
                  {timerColor === option.value && (
                    <div className="absolute -top-1 -right-1 text-primary">
                      <Check className="h-2.5 w-2.5" />
                    </div>
                  )}
                </Button>
              );
            })}
          </div>
        </div>
      </div>
      
      {/* Background Opacity Section */}
      <div className="space-y-1.5 pb-1">
        <div className="flex items-center justify-between">
          <h3 className="text-xs font-medium text-foreground flex items-center gap-1">
            <Circle className="h-3 w-3 text-primary/70" />
            <span>Background Opacity</span>
          </h3>
          <Badge variant="outline" className="text-[9px] px-1 py-0 h-4 font-normal border-primary/20 bg-background">
            {timerOpacity}%
          </Badge>
        </div>
        <div className="bg-muted/30 p-3 pb-2 rounded-lg border border-border/40">
          <Slider
            defaultValue={[timerOpacity]}
            max={100}
            step={5}
            className="w-full"
            onValueChange={handleOpacityChange}
          />
          <div className="flex justify-between mt-1">
            <span className="text-[9px] text-muted-foreground">Transparent</span>
            <span className="text-[9px] text-muted-foreground">Solid</span>
          </div>
        </div>
      </div>
    </div>
  );
} 