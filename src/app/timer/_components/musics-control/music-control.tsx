'use client';

import React, { useState, createContext, useContext } from 'react';
import { cn } from '@/lib/utils';
import { MusicControlProps, TabType } from './types';
import { SystemPlayer } from './system-player';
import { YouTubePlayer } from './youtube-player';
import { MyPlaylistPlayer } from './my-playlist-player';
import { Settings, PlayCircle, Music } from 'lucide-react';

// Audio Management Context
interface AudioContextType {
  pauseSystemAudio: () => void;
  pauseYouTubeAudio: () => void;
}

const AudioContext = createContext<AudioContextType | null>(null);

export function MusicControl({ playlist, className }: MusicControlProps) {
  const [activeTab, setActiveTab] = useState<TabType>('system');
  const [systemPlayerRef, setSystemPlayerRef] = useState<any>(null);
  const [youtubePlayerRef, setYoutubePlayerRef] = useState<any>(null);

  const tabs = [
    { id: 'system' as TabType, label: 'System', icon: Settings },
    { id: 'social' as TabType, label: 'YouTube', icon: PlayCircle },
    { id: 'spotify' as TabType, label: 'My Playlist', icon: Music }
  ];

  const audioManager: AudioContextType = {
    pauseSystemAudio: () => {
      if (systemPlayerRef?.pauseAll) {
        systemPlayerRef.pauseAll();
      }
    },
    pauseYouTubeAudio: () => {
      if (youtubePlayerRef?.pauseVideo) {
        youtubePlayerRef.pauseVideo();
      }
    }
  };



  // Keep all players mounted but show/hide them to prevent audio interruption
  const renderAllTabContent = () => {
    return (
      <div className="relative">
        {/* System Player */}
        <div className={cn(
          "transition-all duration-200",
          activeTab === 'system' ? 'block' : 'hidden'
        )}>
          <SystemPlayer 
            playlist={playlist} 
            onPlayerRef={setSystemPlayerRef}
          />
        </div>

        {/* YouTube Player */}
        <div className={cn(
          "transition-all duration-200",
          activeTab === 'social' ? 'block' : 'hidden'
        )}>
          <YouTubePlayer onPlayerRef={setYoutubePlayerRef} />
        </div>

        {/* My Playlist Player */}
        <div className={cn(
          "transition-all duration-200",
          activeTab === 'spotify' ? 'block' : 'hidden'
        )}>
          <MyPlaylistPlayer />
        </div>
      </div>
    );
  };

  return (
    <AudioContext.Provider value={audioManager}>
      <div
        className={cn(
          "bg-background/95 backdrop-blur-sm rounded-lg border shadow-lg",
          "w-full max-w-[370px] mx-auto z-[1001] music-control-container",
          "transform-gpu", // Hardware acceleration for better mobile performance
          className
        )}
        role="region"
        aria-label="Music Control"
      >
        {/* Optimized Mobile Tab Navigation */}
        <div className="flex border-b border-border/30 overflow-x-auto no-scrollbar">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  "flex-1 flex items-center justify-center gap-1.5 px-2 sm:px-3 py-2",
                  "border-b-2 transition-all duration-200 min-w-0 whitespace-nowrap",
                  isActive 
                    ? "border-primary text-primary bg-primary/5" 
                    : "border-transparent text-muted-foreground hover:text-foreground hover:bg-muted/20"
                )}
                aria-selected={isActive}
                role="tab"
              >
                <Icon className="h-3.5 w-3.5 shrink-0" />
                <span className="text-xs font-medium truncate">{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Mobile-optimized Content Area with vertical scrolling */}
        <div
          className="p-2 sm:p-3 max-h-[60vh] sm:max-h-[50vh] overflow-y-auto overscroll-contain touch-pan-y music-control-scroll"
          style={{
            WebkitOverflowScrolling: 'touch',
            scrollbarWidth: 'thin' // For Firefox
          }}
        >
          {renderAllTabContent()}
        </div>
      </div>
    </AudioContext.Provider>
  );
}

export const useAudioContext = () => {
  const context = useContext(AudioContext);
  if (!context) {
    throw new Error('useAudioContext must be used within AudioContext Provider');
  }
  return context;
};