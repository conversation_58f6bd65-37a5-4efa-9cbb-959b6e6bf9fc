import { useEffect, useCallback, useRef, useState } from 'react';
import { usePomodoroStore } from '@/lib/pomodoro-store';
import { notificationService } from '@/lib/notification-service';
import { useNotificationStore } from '@/lib/notification-store';
import { useCreatePomodoroSession, type BulkTransferSession } from '@schemas/Pomodoro';
import { MINIMUM_FOCUS_DURATION_SECONDS, MINIMUM_BREAK_DURATION_SECONDS, type InterruptedSession } from '@schemas/Pomodoro/pomodoro-type';
import { useUserStore } from '@/store/userStore';
import { toast } from 'sonner';
import { useLocalPomodoroStore } from '@/lib/local-pomodoro-store';
import { useLocalTaskStore } from '@/lib/local-task-store';
import { useCoordinatedSync } from './useCoordinatedSync';

// Module-level variable to ensure only one timer instance exists
let globalIntervalId: NodeJS.Timeout | null = null;

// Threshold in seconds before timer ends to preload sound
const PRELOAD_SOUND_THRESHOLD = 10;

// Track recorded session IDs to avoid duplicates (globally)
const recordedSessions = new Set<string>();

// Note: Transfer coordination is now handled by useCoordinatedSync

interface UsePomodoroOptions {
  onSessionComplete?: (phase: string, isAuthenticated: boolean) => void;
}

export function usePomodoro(options?: UsePomodoroOptions) {
  const {
    isRunning,
    timeRemaining,
    currentPhase,
    tick,
    startTimer,
    pauseTimer,
    resetTimer,
    skipBreak,
    completedSessions,
    timerSettings,
    pomodoroCount,
    sessionStartTime,
    recordSession,
    currentTask,
  } = usePomodoroStore();

  const prevTimeRef = useRef<number>(timeRemaining);
  const isFirstRenderRef = useRef<boolean>(true);
  const soundPreloadedRef = useRef<boolean>(false);
  const prevPhaseRef = useRef<string>(currentPhase);
  const prevIsRunningRef = useRef<boolean>(isRunning);
  const lastToastTimeRef = useRef<number>(0);
  const createPomodoroSession = useCreatePomodoroSession();

  // Get authentication status from user store and sync status
  const { isAuthenticated } = useUserStore();
  const { syncStatus } = useCoordinatedSync();

  // Get local storage functions
  const { addSession: addLocalSession, exportSessions, clearAllSessions } = useLocalPomodoroStore();
  
  // Get local task store for linking local sessions to local tasks
  const { getTaskById: getLocalTaskById } = useLocalTaskStore();

  // State for tracking interruption periods
  const [interruptedSessions, setInterruptedSessions] = useState<InterruptedSession[]>([]);
  const [currentInterruptionStart, setCurrentInterruptionStart] = useState<Date | null>(null);

  // Format the time remaining into a MM:SS format
  const formattedTime = useCallback(() => {
    // For count up mode in pomodoro phase, show elapsed time instead of remaining time
    const isCountUpMode = currentPhase === 'pomodoro' && timerSettings.timerMode === 'countUp';

    let displayTime: number;
    if (isCountUpMode) {
      // Calculate elapsed time from the start of the session
      const totalTime = timerSettings.pomodoroMinutes * 60;
      displayTime = totalTime - timeRemaining;
    } else {
      // Normal countdown mode - show remaining time
      displayTime = timeRemaining;
    }

    const minutes = Math.floor(displayTime / 60);
    const seconds = displayTime % 60;
    return `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  }, [timeRemaining, currentPhase, timerSettings]);

  // Get phase label for display
  const phaseLabel = useCallback(() => {
    switch (currentPhase) {
      case 'pomodoro':
        // Show task name if there's a current task, otherwise show default label
        return currentTask ? currentTask.title : 'Focus Time';
      case 'shortBreak':
        return 'Short Break';
      case 'longBreak':
        return 'Long Break';
      default:
        return currentTask ? currentTask.title : 'Focus Time';
    }
  }, [currentPhase, currentTask]);

  // Progress percentage (0-100)
  const progressPercentage = useCallback(() => {
    // For count up mode in pomodoro phase, return 0 to hide progress bar or show indefinite progress
    const isCountUpMode = currentPhase === 'pomodoro' && timerSettings.timerMode === 'countUp';

    if (isCountUpMode) {
      // Return 0 for count up mode to indicate no predetermined progress
      return 0;
    }

    let totalTime: number;
    switch (currentPhase) {
      case 'pomodoro':
        totalTime = timerSettings.pomodoroMinutes * 60;
        break;
      case 'shortBreak':
        totalTime = timerSettings.shortBreakMinutes * 60;
        break;
      case 'longBreak':
        totalTime = timerSettings.longBreakMinutes * 60;
        break;
      default:
        totalTime = timerSettings.pomodoroMinutes * 60;
    }
    return 100 - Math.floor((timeRemaining / totalTime) * 100);
  }, [timeRemaining, currentPhase, timerSettings]);

  // Handle fullscreen functionality
  const handleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch(err => {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      });
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }, []);

  // Update document title with current timer
  useEffect(() => {
    document.title = `${formattedTime()} - ${phaseLabel()} | Pomodoro 365`;

    return () => {
      document.title = 'Pomodoro 365 - Focus Better with Immersive Background Videos';
    };
  }, [timeRemaining, currentPhase, formattedTime, phaseLabel]);

  // Preload sound when timer is about to end
  useEffect(() => {
    if (isRunning && timeRemaining <= PRELOAD_SOUND_THRESHOLD && !soundPreloadedRef.current) {
      // Preload sound when timer is about to end
      useNotificationStore.getState().preloadSound();
      soundPreloadedRef.current = true;
      console.log('Sound preloaded for timer completion');
    } else if (timeRemaining > PRELOAD_SOUND_THRESHOLD && soundPreloadedRef.current) {
      // Reset preload flag when timer is not close to ending
      soundPreloadedRef.current = false;
    }
  }, [timeRemaining, isRunning]);

  // Track pause/resume actions to record interruption periods
  useEffect(() => {
    // Only track if we have a valid session start time
    if (!sessionStartTime) {
      return;
    }

    // Detect when timer is paused (was running, now stopped)
    if (prevIsRunningRef.current && !isRunning) {
      const now = new Date();
      setCurrentInterruptionStart(now);
      console.log('Timer paused - interruption started at:', now.toISOString());
    }

    // Detect when timer is resumed (was stopped, now running)
    if (!prevIsRunningRef.current && isRunning && currentInterruptionStart) {
      const now = new Date();
      const newInterruption: InterruptedSession = {
        startTime: currentInterruptionStart,
        endTime: now
      };

      setInterruptedSessions(prev => [...prev, newInterruption]);
      setCurrentInterruptionStart(null);
      console.log('Timer resumed - interruption recorded:', {
        startTime: currentInterruptionStart.toISOString(),
        endTime: now.toISOString()
      });
    }

    // Update previous running state
    prevIsRunningRef.current = isRunning;
  }, [isRunning, sessionStartTime, currentInterruptionStart]);

  // Reset interruption tracking when a new session starts
  useEffect(() => {
    // Reset interruption tracking when session starts or phase changes
    if (sessionStartTime) {
      setInterruptedSessions([]);
      setCurrentInterruptionStart(null);
      console.log('New session started - reset interruption tracking');
    }
  }, [sessionStartTime, currentPhase]);

  // Record any type of session (focus or break)
  const recordPomodoroSession = useCallback(async (isCompleted: boolean, isInterrupted: boolean, phaseType: string) => {
    // Skip if there's no sessionStartTime
    if (!sessionStartTime) {
      console.log('Not recording session - no session start time');
      return;
    }

    // Ensure sessionStartTime is a valid Date object before proceeding
    let sessionStartDate: Date;
    try {
      // If it's a Date object, use it directly
      if (sessionStartTime instanceof Date) {
        sessionStartDate = sessionStartTime;
      }
      // If it's a string that contains a valid date, convert it
      else if (typeof sessionStartTime === 'string') {
        sessionStartDate = new Date(sessionStartTime);
      }
      // Otherwise try to create a new Date object from it
      else {
        sessionStartDate = new Date(sessionStartTime);
      }

      // Validate the date is valid
      if (isNaN(sessionStartDate.getTime())) {
        throw new Error('Invalid date value');
      }
    } catch (error) {
      console.error('Invalid sessionStartTime:', sessionStartTime, error);
      return;
    }

    // Handle any pending interruption (if user is still paused when session ends)
    const finalInterruptedSessions = [...interruptedSessions];
    if (currentInterruptionStart) {
      const now = new Date();
      finalInterruptedSessions.push({
        startTime: currentInterruptionStart,
        endTime: now
      });
      console.log('Session ended during pause - added final interruption');
    }

    // Determine if session was actually interrupted (either manually or had pause/resume cycles)
    const wasInterrupted = isInterrupted || finalInterruptedSessions.length > 0;

    // Capture values before state changes
    const startTime = sessionStartDate;
    const endTime = new Date();
    const actualDuration = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);

    // Calculate interruption duration
    const totalInterruptionDuration = finalInterruptedSessions.reduce((total, interruption) => {
      const interruptStart = new Date(interruption.startTime);
      const interruptEnd = new Date(interruption.endTime);
      return total + Math.floor((interruptEnd.getTime() - interruptStart.getTime()) / 1000);
    }, 0);

    // Calculate actual focus/break duration (excluding interruptions)
    const actualActiveDuration = actualDuration - totalInterruptionDuration;

    // Get the maximum allowed duration for this phase from timer settings
    const getMaxDurationForPhase = (phase: string): number => {
      switch (phase) {
        case 'pomodoro':
          return timerSettings.pomodoroMinutes * 60;
        case 'shortBreak':
          return timerSettings.shortBreakMinutes * 60;
        case 'longBreak':
          return timerSettings.longBreakMinutes * 60;
        default:
          return timerSettings.pomodoroMinutes * 60;
      }
    };

    // Cap the active duration to the maximum timer setting to prevent exceeding intended duration
    // Exception: In count up mode for pomodoro phase, don't cap the duration
    const maxAllowedDuration = getMaxDurationForPhase(phaseType);
    const isCountUpMode = phaseType === 'pomodoro' && timerSettings.timerMode === 'countUp';
    const cappedActiveDuration = isCountUpMode ? actualActiveDuration : Math.min(actualActiveDuration, maxAllowedDuration);

    // Convert phase type to PomodoroType enum value
    const intervalTypeMap: Record<string, string> = {
      'pomodoro': 'FOCUS',
      'shortBreak': 'SHORT_BREAK',
      'longBreak': 'LONG_BREAK'
    };

    // Get the correct interval type
    const intervalType = intervalTypeMap[phaseType] || 'FOCUS';

    // Create a unique ID based on session start time, end time, and phase type
    const sessionStartTimeStr = sessionStartDate.toISOString();
    const sessionId = `${sessionStartTimeStr}-${endTime.toISOString()}-${phaseType}-${intervalType}-${actualDuration}-${isCompleted ? 'completed' : 'interrupted'}`;

    // If we've already recorded this session, don't record it again
    if (recordedSessions.has(sessionId)) {
      console.log('Session already recorded, skipping:', sessionId);
      return;
    }

    // Mark this session as recorded immediately
    recordedSessions.add(sessionId);

    // Get the maximum allowed duration for this phase from timer settings
    const getMinimumDuration = (sessionType: string): number => {
      if (sessionType === 'FOCUS') {
        return MINIMUM_FOCUS_DURATION_SECONDS;
      } else if (sessionType === 'SHORT_BREAK' || sessionType === 'LONG_BREAK') {
        return MINIMUM_BREAK_DURATION_SECONDS;
      }
      return 0;
    };

    const minimumDuration = getMinimumDuration(intervalType);

    // Check if the capped active duration meets the minimum requirement
    if (cappedActiveDuration < minimumDuration) {
      console.log(`Session too short to record - ${intervalType} session duration ${cappedActiveDuration}s is less than minimum ${minimumDuration}s`);
      // Remove from recorded sessions since we're not actually recording it
      recordedSessions.delete(sessionId);

      // Only show toast notification for authenticated users or when local sessions exist
      if (isAuthenticated || !isAuthenticated) {
        // Debounce toast to prevent duplicates within 2 seconds
        const now = Date.now();
        if (now - lastToastTimeRef.current < 2000) {
          console.log('Skipping duplicate toast - too soon since last toast');
          return;
        }
        lastToastTimeRef.current = now;

        // Show user-friendly toast notification
        const sessionTypeLabel = intervalType === 'FOCUS' ? 'Focus' : 'Break';
        const minimumMinutes = Math.ceil(minimumDuration / 60);
        const actualMinutes = Math.floor(cappedActiveDuration / 60);
        const actualSeconds = cappedActiveDuration % 60;
        const actualTimeString = actualMinutes > 0
          ? `${actualMinutes}m ${actualSeconds}s`
          : `${actualSeconds}s`;

        toast.info(`Session Not Recorded`, {
          description: `${sessionTypeLabel} sessions need at least ${minimumMinutes} minute${minimumMinutes > 1 ? 's' : ''} to be saved. Your session was only ${actualTimeString}.`,
          duration: 5000,
        });
      }

      return;
    }

    console.log('Recording pomodoro session', {
      sessionId,
      phaseType,
      intervalType,
      isCompleted,
      isInterrupted: wasInterrupted,
      interruptedSessions: finalInterruptedSessions,
      totalDuration: actualDuration,
      activeDuration: actualActiveDuration,
      cappedActiveDuration: cappedActiveDuration,
      maxAllowedDuration: maxAllowedDuration,
      minimumDuration: minimumDuration,
      interruptionDuration: totalInterruptionDuration,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      isAuthenticated
    });

    // Update store state immediately
    recordSession();

    // Save to local storage only if user is not authenticated
    // When authenticated, sessions are saved directly to database
    if (!isAuthenticated) {
      console.log('User not authenticated - saving to local storage');
      
      // For local sessions, verify the task exists in local storage if taskId is provided
      let validTaskId = currentTask?.id || null;
      if (validTaskId && validTaskId.startsWith('local_task_')) {
        const localTask = getLocalTaskById(validTaskId);
        if (!localTask) {
          console.warn('Local task not found, unlinking from session:', validTaskId);
          validTaskId = null;
        }
      }

      const localSession = {
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        totalDuration: actualDuration,
        focusDuration: intervalType === 'FOCUS' ? cappedActiveDuration : null,
        breakDuration: (intervalType === 'SHORT_BREAK' || intervalType === 'LONG_BREAK') ? cappedActiveDuration : null,
        intervalType: intervalType as 'FOCUS' | 'SHORT_BREAK' | 'LONG_BREAK',
        completed: isCompleted,
        interrupted: wasInterrupted,
        note: null,
        interruptedSessions: finalInterruptedSessions.length > 0 ? finalInterruptedSessions.map(session => ({
          startTime: session.startTime instanceof Date ? session.startTime.toISOString() : session.startTime,
          endTime: session.endTime instanceof Date ? session.endTime.toISOString() : session.endTime,
        })) : null,
        taskId: validTaskId,
      };

      addLocalSession(localSession);
      return;
    }

    // If user is authenticated, save to database
    try {
      // Create session data object
      const sessionData = {
        totalDuration: actualDuration.toString(),
        // Set focus or break duration based on session type, using capped duration
        focusDuration: intervalType === 'FOCUS' ? cappedActiveDuration.toString() : undefined,
        breakDuration: (intervalType === 'SHORT_BREAK' || intervalType === 'LONG_BREAK') ? cappedActiveDuration.toString() : undefined,
        startTime: startTime.toISOString(),
        endTime: endTime.toISOString(),
        completed: isCompleted.toString(),
        interrupted: wasInterrupted.toString(),
        interruptedSessions: finalInterruptedSessions.length > 0 ? JSON.stringify(finalInterruptedSessions) : undefined,
        intervalType,
        taskId: currentTask?.id || undefined,
      };

      // Send the API request
      await createPomodoroSession.mutateAsync({
        form: sessionData
      });
    } catch (error) {
      console.error('Error recording pomodoro session:', error);
      // Remove from recorded sessions if it failed so we can try again later
      recordedSessions.delete(sessionId);
    }
  }, [sessionStartTime, recordSession, createPomodoroSession, interruptedSessions, currentInterruptionStart, timerSettings, isAuthenticated, addLocalSession, currentTask, getLocalTaskById]);

  // Track phase changes to trigger notifications and record sessions
  useEffect(() => {
    // Check if time hit zero (from previous value being non-zero)
    if (prevTimeRef.current > 0 && timeRemaining === 0) {
      // For count up mode in pomodoro phase, don't auto-complete - user must manually stop
      const isCountUpMode = currentPhase === 'pomodoro' && timerSettings.timerMode === 'countUp';

      if (!isCountUpMode) {
        // If we have a valid session start time, record the session
        if (sessionStartTime) {
          // Record the session with the current phase type
          recordPomodoroSession(true, false, currentPhase);

          // Trigger session complete callback
          options?.onSessionComplete?.(currentPhase, isAuthenticated);

          // Also handle notifications for break completion
          if (currentPhase === 'shortBreak') {
            notificationService?.notifyBreakComplete(false);
          } else if (currentPhase === 'longBreak') {
            notificationService?.notifyBreakComplete(true);
          }
        }
      }
    }

    // Keep track of the previous timeRemaining value
    prevTimeRef.current = timeRemaining;
    // Store the current phase for next render
    prevPhaseRef.current = currentPhase;
  }, [timeRemaining, currentPhase, sessionStartTime, options, isAuthenticated, recordPomodoroSession, timerSettings]);

  // Record interrupted session when user manually resets or skips
  const handleResetTimer = useCallback(() => {
    if (isRunning && sessionStartTime) {
      // Record session but let the function determine if it was interrupted based on pause/resume cycles
      // Pass false for isInterrupted - let recordPomodoroSession determine based on finalInterruptedSessions
      recordPomodoroSession(false, false, currentPhase);
    }

    // Clear interruption tracking when resetting
    setInterruptedSessions([]);
    setCurrentInterruptionStart(null);

    resetTimer();
  }, [isRunning, currentPhase, recordPomodoroSession, resetTimer, sessionStartTime]);

  // Override skip break to potentially record interrupted sessions
  const handleSkipBreak = useCallback(() => {
    if (isRunning && sessionStartTime && (currentPhase === 'shortBreak' || currentPhase === 'longBreak')) {
      // Record the break but let the function determine if it was interrupted based on pause/resume cycles
      // Pass false for isInterrupted - let recordPomodoroSession determine based on finalInterruptedSessions
      recordPomodoroSession(false, false, currentPhase);
    }

    // Clear interruption tracking when skipping
    setInterruptedSessions([]);
    setCurrentInterruptionStart(null);

    skipBreak();
  }, [skipBreak, isRunning, sessionStartTime, currentPhase, recordPomodoroSession]);

  // Wrapper for start timer to handle interruption state
  const handleStartTimer = useCallback(() => {
    startTimer();
  }, [startTimer]);

  // Wrapper for pause timer to handle interruption state
  const handlePauseTimer = useCallback(() => {
    pauseTimer();
  }, [pauseTimer]);

  // Set up the interval for the timer with a singleton approach
  useEffect(() => {
    // Setup function to create the timer
    const setupTimer = () => {
      // Clean up any existing global timer
      if (globalIntervalId) {
        clearInterval(globalIntervalId);
        globalIntervalId = null;
      }

      // Create a new global timer that checks running state on each tick
      globalIntervalId = setInterval(() => {
        if (isRunning) {
          tick();
        }
      }, 1000);
    };

    // Only set up the timer on the first render
    if (isFirstRenderRef.current) {
      isFirstRenderRef.current = false;
      setupTimer();
    } else {
      // Re-setup timer when dependencies change
      setupTimer();
    }

    // Clean up on component unmount
    return () => {
      if (globalIntervalId) {
        clearInterval(globalIntervalId);
        globalIntervalId = null;
      }
    };
  }, [isRunning, tick]); // Include isRunning and tick in dependency array

  // Note: Session transfer is now handled by useCoordinatedSync hook
  // which ensures tasks are synced before sessions

  // Clear recorded sessions when authentication status changes
  useEffect(() => {
    // Clear recorded sessions when user logs out
    if (!isAuthenticated) {
      recordedSessions.clear();
      console.log('User logged out - cleared recorded sessions');
    }
  }, [isAuthenticated]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Clear any pending records on unmount
      recordedSessions.clear();
    };
  }, []);

  // Return timer control functions and state
  return {
    isRunning,
    timeRemaining,
    currentPhase,
    formattedTime: formattedTime(),
    phaseLabel: phaseLabel(),
    progressPercentage: progressPercentage(),
    startTimer: handleStartTimer,
    pauseTimer: handlePauseTimer,
    resetTimer: handleResetTimer,
    skipBreak: handleSkipBreak,
    completedSessions,
    handleFullscreen,
    pomodoroCount,
  };
}